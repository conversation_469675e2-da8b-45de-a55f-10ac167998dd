<!DOCTYPE html>
<html>
<head>
    <title>测试图片上传</title>
    <script src="/assets/libs/tinymce/tinymce.min.js"></script>
</head>
<body>
    <h1>测试TinyMCE图片上传</h1>
    <textarea id="editor"></textarea>
    
    <script>
        tinymce.init({
            selector: '#editor',
            height: 400,
            plugins: 'image',
            toolbar: 'image',
            menubar: false,
            
            // 图片上传配置
            automatic_uploads: true,
            file_picker_types: 'image',
            image_advtab: true,
            image_title: true,
            image_caption: true,
            
            // 自定义图片上传处理器
            images_upload_handler: function (blobInfo, success, failure, progress) {
                console.log('开始上传图片:', blobInfo.filename());
                
                var xhr, formData;
                
                xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', '/admin/emailMessages/uploadImage');
                
                xhr.upload.onprogress = function (e) {
                    if (progress && e.lengthComputable) {
                        var percent = e.loaded / e.total * 100;
                        console.log('上传进度:', percent + '%');
                        progress(percent);
                    }
                };
                
                xhr.onload = function() {
                    console.log('上传响应状态:', xhr.status);
                    console.log('上传响应内容:', xhr.responseText);
                    
                    if (xhr.status === 403) {
                        failure('HTTP Error: ' + xhr.status, { remove: true });
                        return;
                    }
                    
                    if (xhr.status < 200 || xhr.status >= 300) {
                        failure('HTTP Error: ' + xhr.status);
                        return;
                    }
                    
                    var json;
                    try {
                        json = JSON.parse(xhr.responseText);
                    } catch (e) {
                        console.error('JSON解析错误:', e);
                        failure('Invalid JSON response: ' + xhr.responseText);
                        return;
                    }
                    
                    if (!json || typeof json.location != 'string') {
                        console.error('无效的JSON响应:', json);
                        failure('Invalid JSON: ' + xhr.responseText);
                        return;
                    }
                    
                    // 确保返回的路径以 / 开头
                    var imageUrl = json.location;
                    if (!imageUrl.startsWith('/')) {
                        imageUrl = '/' + imageUrl;
                    }
                    
                    console.log('图片上传成功，URL:', imageUrl);
                    success(imageUrl);
                };
                
                xhr.onerror = function () {
                    console.error('XHR错误:', xhr.status);
                    failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                };
                
                xhr.ontimeout = function () {
                    console.error('上传超时');
                    failure('Image upload failed due to timeout');
                };
                
                // 设置超时时间
                xhr.timeout = 30000; // 30秒
                
                formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());
                
                xhr.send(formData);
            }
        });
    </script>
</body>
</html>
